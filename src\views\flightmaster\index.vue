<template>
  <div class="flight-container">
    <div class="map-wrapper">
      <Amap ref="amap" :coordinates="coordinates" :zoom="17" />

      <div class="video-containers-wrapper">
        <div v-for="(video, index) in videos" :key="index" class="video-container-wrapper">
          <div class="video-container">
            <!-- 调试按钮 -->
            <div class="video-debug-header">
              <el-button type="text" icon="el-icon-setting" @click="toggleDebugPanel(index)" class="debug-btn"
                title="调试" size="mini">
                调试
              </el-button>
            </div>

            <TCPlayerVideo :src="video.src" :device-name="video.deviceName" :width="400" :height="250" :autoplay="false"
              :muted="true" :license-url="tcPlayerLicenseUrl" @close="closeVideo(index)" />
          </div>

          <!-- 调试面板 -->
          <div v-if="video.showDebugPanel" class="debug-panel">
            <div class="debug-panel-header">
              <span class="debug-title">调试信息</span>
              <el-button type="text" icon="el-icon-close" @click="closeDebugPanel(index)" class="debug-close-btn"
                size="mini" />
            </div>

            <div class="debug-panel-content">
              <div v-if="video.debugLoading" class="debug-loading">
                <i class="el-icon-loading"></i>
                <span>加载调试信息中...</span>
              </div>

              <div v-else-if="video.debugError" class="debug-error">
                <i class="el-icon-warning"></i>
                <span>{{ video.debugError }}</span>
                <el-button type="primary" size="mini" @click="refreshDebugInfo(index)" style="margin-top: 10px;">
                  重试
                </el-button>
              </div>

              <div v-else-if="video.debugInfo" class="debug-info">
                <div class="debug-sections-horizontal">
                  <!-- Dock 调试区域 -->
                  <div class="debug-section-left">
                    <div class="debug-section-title">机场</div>
                    <div v-for="item in video.debugInfo.dock" :key="item.title" class="debug-control-item">
                      <div class="debug-control-row">
                        <div class="debug-control-label">{{ item.title }}</div>

                        <!-- 滑动开关 (list长度为2) -->
                        <div v-if="item.list && item.list.length === 2" class="debug-control-widget">
                          <DebugSwitch :value="item.val === '1'" :active-text="getSwitchLabel(item.list, '1') || '开启'"
                            :inactive-text="getSwitchLabel(item.list, '0') || '关闭'"
                            @change="handleSwitchChange(video.deviceSn, getSwitchTargetUrl(item.list, $event), getSwitchTargetValue(item.list, $event))" />
                        </div>

                        <!-- 下拉选择框 (list长度大于2) -->
                        <div v-else-if="item.list && item.list.length > 2" class="debug-control-widget">
                          <el-select :value="item.val"
                            @change="handleSwitchChange(video.deviceSn, getSwitchTargetUrl(item.list, $event), $event)"
                            placeholder="请选择" size="mini">
                            <el-option v-for="option in item.list" :key="option.value" :label="option.label"
                              :value="option.value" />
                          </el-select>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Drone 调试区域 -->
                  <div class="debug-section-right">
                    <div class="debug-section-title">飞行器</div>
                    <div v-for="item in video.debugInfo.drone" :key="item.title" class="debug-control-item">
                      <div class="debug-control-row">
                        <div class="debug-control-label">{{ item.title }}</div>

                        <!-- 滑动开关 (list长度为2) -->
                        <div v-if="item.list && item.list.length === 2" class="debug-control-widget">
                          <DebugSwitch :value="item.val === '1'" :active-text="getSwitchLabel(item.list, '1') || '开启'"
                            :inactive-text="getSwitchLabel(item.list, '0') || '关闭'"
                            @change="handleSwitchChange(video.deviceSn, getSwitchTargetUrl(item.list, $event), getSwitchTargetValue(item.list, $event))" />
                        </div>

                        <!-- 下拉选择框 (list长度大于2) -->
                        <div v-else-if="item.list && item.list.length > 2" class="debug-control-widget">
                          <el-select :value="item.val"
                            @change="handleSwitchChange(video.deviceSn, getSwitchTargetUrl(item.list, $event), $event)"
                            placeholder="请选择" size="mini">
                            <el-option v-for="option in item.list" :key="option.value" :label="option.label"
                              :value="option.value" />
                          </el-select>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div v-else class="debug-empty">
                <span>暂无调试信息</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>


    <div class="flight-sidebar">
      <div class="flight-sidebar-header">
        <el-row type="flex" align="middle">
          <el-col :span="24">
            <svg-icon icon-class="jichang_list" class="sidebar-icon" />
            设备列表
          </el-col>
        </el-row>
      </div>
      <div class="flight-airport-list">


        <div v-for="(coord, index) in deviceList" :key="index">
          <el-card class="flight-airport-item" shadow="hover" @click.native="handleDeviceClick(coord)">
            <!-- 头部信息 -->
            <el-row type="flex" align="middle">
              <el-col :span="24">
                <div class="airport-title">
                  <div class="airport-image" style="width: 2rem; height: 2rem;">
                    <img src="@/assets/images/wrj_jc.png" alt="机场图片" class="airport-img" />
                  </div>
                  <div style="padding-left: 1rem;font-size: 1rem;">{{ coord.nickname }}</div>

                  <el-tooltip class="item" effect="dark" content="定位" placement="right">
                    <el-button type="text" icon="el-icon-map-location" @click.stop="locateAirport(coord)"
                      class="locate-button" />
                  </el-tooltip>
                </div>
              </el-col>
            </el-row>
            <!-- 机场信息 -->
            <div class="airport-divider"></div>
            <el-row style="margin: 0.5rem 0px;">
              <el-col :span="7" class="dockstate">
                <el-tag size="medium" :type="getStateType(coord.mode_code, 3)"
                  style="width: 100%; display: flex; justify-content: center;">
                  {{ getStateLabel(coord.mode_code, 3) }}
                </el-tag>
              </el-col>
              <el-col :span="17">
                <el-tooltip class="item" effect="dark" :content="coord.task_name" placement="top"
                  :disabled="!isTextOverflow">
                  <div class="task-name" ref="taskName" @mouseenter="checkTextOverflow">
                    {{ coord.task_name }}
                  </div>
                </el-tooltip>
              </el-col>
            </el-row>
            <!-- 无人机信息 -->
            <el-row>
              <el-col :span="7" class="dockstate">
                <el-tag size="medium" :type="getStateType('', 0)"
                  style="width: 100%; display: flex; justify-content: center;">
                  {{ getStateLabel("", 0) }}
                </el-tag>
              </el-col>
              <el-col :span="17">
                <el-tooltip class="item" effect="dark" :content="coord.children.nickname" placement="top"
                  :disabled="!isTextOverflow">
                  <div class="task-name" ref="taskName" @mouseenter="checkTextOverflow">
                    {{ coord.children.nickname }}
                  </div>
                </el-tooltip>
              </el-col>
            </el-row>
          </el-card>

          <!-- -----飞机部分----- -->
          <el-card class="flight-airport-item" shadow="hover" @click.native="handleDeviceClick(coord.children)">
            <!-- 头部信息 -->
            <el-row type="flex" align="middle">
              <el-col :span="24">
                <div class="airport-title">
                  <div class="airport-image" style="width: 2rem; height: 2rem;">
                    <img src="@/assets/images/wrj.png" alt="无人机图片" class="airport-img" />
                  </div>
                  <div style="padding-left: 1rem;font-size: 1rem;">{{ coord.children.nickname }}</div>

                  <el-tooltip class="item" effect="dark" content="定位" placement="right">
                    <el-button type="text" icon="el-icon-map-location" @click.stop="locateAirport(coord)"
                      class="locate-button" />
                  </el-tooltip>
                </div>
              </el-col>
            </el-row>
            <!--  -->
            <div class="airport-divider"></div>
            <el-row style="margin: 0.5rem 0px;">

            </el-row>
            <!-- 无人机信息 -->
            <el-row>
            </el-row>
          </el-card>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import { mapActions } from 'vuex'
import { getDicts, rectangle } from '@/api/system/dict/data'
import { deviceInfo } from '@/api/flightmaster/flightmaster'
import { getDebugInfo, sendDebugCommand, openDebugMode, closeDebugMode } from '@/api/flightmaster/debug'
import { getToken } from "@/utils/auth";
import socketService from '../../../socket/index'
import axios from 'axios'; // 引入 axios
import TCPlayerVideo from '@/components/TCPlayerVideo.vue'
import DebugSwitch from '@/components/DebugSwitch.vue'

// 常量定义
const PI = 3.14159265358979324;
const A = 6378245.0;
const EE = 0.00669342162296594323;

export default {
  components: {
    TCPlayerVideo,
    DebugSwitch
  },
  data() {
    return {
      coordinates: [
        [113.091931, 28.264330],
        [113.092931, 28.265330],
        // 添加更多坐标
      ],
      droneModeCode: 0,
      dockModeCode: 1,
      deviceList: [
        {
          "device_sn": "7CTDM3900BM6WS",
          "device_name": "DJI Dock2",
          "workspace_id": "103",
          "control_source": "A",
          "device_desc": "",
          "child_device_sn": "1581F6Q8D242S00C6DVG",
          "domain": 3,
          "type": 2,
          "sub_type": 0,
          "icon_url": {
            "normal_icon_url": "",
            "selected_icon_url": ""
          },
          "status": true,
          "bound_status": true,
          "login_time": "2025-02-23 22:27:47",
          "bound_time": "2025-02-23 22:28:11",
          "nickname": "我的机场",
          "firmware_version": "10.01.1612",
          "workspace_name": "",
          "children": {
            "device_sn": "1581F6Q8D242S00C6DVG",
            "device_name": "M3TD",
            "workspace_id": "103",
            "control_source": "A",
            "device_desc": "",
            "child_device_sn": "",
            "domain": 0,
            "type": 91,
            "sub_type": 1,
            "payloads_list": [
              {
                "payload_sn": "1581F6Q8D242S00C6DVG-0",
                "payload_name": "M3TD Camera",
                "index": 0,
                "control_source": "A",
                "payload_index": "81-0-0"
              }
            ],
            "icon_url": {
              "normal_icon_url": "",
              "selected_icon_url": ""
            },
            "status": false,
            "bound_status": true,
            "login_time": "2025-02-24 13:37:08",
            "bound_time": "2025-02-24 13:36:50",
            "nickname": "松安2号M3TD无人机",
            "firmware_version": "10.01.1612",
            "workspace_name": "",
            "firmware_status": 1,
            "thing_version": "1.3.0"
          },
          "firmware_status": 1,
          "thing_version": "1.3.0",
          "mode_code": 0,
          "task_name": "暂无任务"
        }
      ],
      // 添加字典数据缓存
      dockStateOptions: [],
      droneStateOptions: [],
      isTextOverflow: false,
      socket: null,
      videos: [],
      videoSources: {  // 存储设备对应的视频源
        "7CTDM3900BM6WS": "webrtc://live2.sahy.cloud/live/4TADL330010050-165-0-7",
        "1581F6Q8D242S00C6DVG": "https://highlight-video.cdn.bcebos.com/video/6s/f70a04de-f809-11ef-a6db-7cd30a6157c0.mp4",
        "4TADL330010050": "webrtc://live2.sahy.cloud/live/4TADL330010050-165-0-7",

        // 示例：支持多种视频流格式
        "demo_rtsp": "rtsp://example.com/stream",
        "demo_hls": "https://example.com/stream.m3u8",
        "demo_webrtc": "webrtc://example.com/stream",
        "demo_flv": "https://example.com/stream.flv"
      },
      cityNames: [], // 新增属性，用于存储城市名称
      infoWindow: null, // 用于存储信息窗体实例
      selectedRegion: { name: '' },
      // TCPlayer License URL (需要申请腾讯云License)
      tcPlayerLicenseUrl: 'https://license.vod2.myqcloud.com/license/v2/1303164718_1/v_cube.license',
    };
  },
  created() {
    // 进入页面时隐藏导航栏
    this.toggleNavbar(false)
    this.getDockStateDict()

    //获取设备列表
    this.getDeviceInfo();

    // 调用获取城市名称的接口
    this.fetchCityNames();
    // 添加 WebSocket 连接
    this.connectWebSocket();
  },

  //* 关闭页面调用的全局方法*/
  beforeDestroy() {
    // 使用 nextTick 确保状态更新后再离开页面
    this.$nextTick(async () => {
      this.toggleNavbar(true);

      // 关闭所有打开的调试模式
      await this.closeAllDebugModes();

      // 断开 WebSocket 连接
      socketService.close();
    });
  },

  async beforeRouteLeave(to, from, next) {
    this.toggleNavbar(true);

    // 关闭所有打开的调试模式
    await this.closeAllDebugModes();

    next();
  },

  mounted() {
    // 在地图加载完成后初始化信息窗体
    this.$nextTick(() => {
      if (this.$refs.amap && this.$refs.amap.map) {
        // 创建信息窗体
        this.infoWindow = new AMap.InfoWindow({
          isCustom: true,  // 使用自定义窗体
          content: this.createInfoWindowContent('', 0), // 初始为空内容
          offset: new AMap.Pixel(0, -10), // 设置偏移量
          closeWhenClickMap: true // 点击地图关闭信息窗体
        });
      }
    });

    // 监听页面刷新/关闭事件，自动关闭所有调试模式
    window.addEventListener('beforeunload', this.handlePageUnload);
  },

  beforeDestroy() {
    // 移除页面刷新监听器
    window.removeEventListener('beforeunload', this.handlePageUnload);
  },

  watch: {
    // 监听调试面板显示状态变化
    videos: {
      handler(newVideos, oldVideos) {
        if (!oldVideos) return;
      },
      deep: true
    }
  },

  methods: {
    ...mapActions('app', ['toggleNavbar']),

    // 获取机场状态字典数据
    async getDockStateDict() {
      try {
        var response = await getDicts('flight_dock_state')
        this.dockStateOptions = response.data
        response = await getDicts('flight_drone_state')
        this.droneStateOptions = response.data
      } catch (error) {
        console.error('获取机场状态字典失败:', error)
      }
    },

    // 获取状态显示文本
    getStateLabel(status, domain) {
      if (domain == 3) {
        const dictData = this.dockStateOptions.find(dict => dict.dictValue === status.toString())
        return dictData ? dictData.dictLabel : '离线'
      } else {
        const dictData = this.droneStateOptions.find(dict => dict.dictValue === status.toString())
        return dictData ? dictData.dictLabel : '离线'
      }
    },


    // 获取状态对应的样式类型
    getStateType(modeCode, domain) {
      if (domain == 3) {
        const dictData = this.dockStateOptions[modeCode];
        if (dictData) {
          return dictData.listClass;
        }
      } else {
        const dictData = this.droneStateOptions[modeCode];
        if (dictData) {
          return dictData.listClass;
        }
      }

      return 'info'; // 默认返回 primary 类型
    },

    // 定位到指定机场
    locateAirport(coord) {
      console.log("定位坐标:", coord);
      if (this.$refs.amap && this.$refs.amap.map) {
        this.$refs.amap.map.setZoomAndCenter(15, coord);
      }
    },
    // 修改处理设备卡片点击的方法
    handleDeviceClick(device) {

      if (!device) {
        console.log("设备对象为空");
        return;
      }

      const videoSrc = this.videoSources[device.device_sn];
      console.log("视频源:", videoSrc);

      // 检查是否已经存在相同设备的视频
      const existingVideoIndex = this.videos.findIndex(v => v.deviceName === device.nickname);

      if (videoSrc) {
        if (existingVideoIndex === -1) {
          // 如果不存在，则添加新视频
          this.videos.push({
            src: videoSrc,
            deviceName: device.nickname,
            deviceSn: device.device_sn,
            showDebugPanel: false,
            debugLoading: false,
            debugError: null,
            debugInfo: null
          });
          console.log("添加视频:", device.nickname);
        } else {
          // 如果已存在，可以选择聚焦到该视频或者提示用户
          console.log("该设备的视频已经在播放");
          // 可以添加一些视觉反馈，比如让对应的视频框闪烁
          const videoElement = document.querySelectorAll('.video-container')[existingVideoIndex];
          if (videoElement) {
            videoElement.classList.add('highlight');
            setTimeout(() => {
              videoElement.classList.remove('highlight');
            }, 1000);
          }
        }
      } else {
        console.log("未找到对应的视频源");
      }
    },

    // 检查文本是否溢出
    checkTextOverflow(e) {
      const element = e.target;
      this.isTextOverflow = element.scrollWidth > element.offsetWidth;
    },

    // 新增连接 WebSocket 的方法
    async connectWebSocket() {
      const token = getToken(); // 获取实际的 Authorization token
      // 这里是 username
      socketService.init("warning-all");
      try {
        const msg = await socketService.getMessage();
        //打印出来 服务器给我的信息
        // console.error(JSON.parse(msg.data) );
      } catch (error) {
        console.error("Error receiving message:", error);
      }

    },

    async closeVideo(index) {
      const video = this.videos[index];
      if (!video) return;

      // 如果调试面板是打开的，先关闭调试模式
      if (video.showDebugPanel && video.deviceSn) {
        console.log('关闭视频前，先关闭调试模式:', video.deviceName);
        try {
          await this.closeDebugModeForDevice(video.deviceSn);
          console.log('调试模式已关闭:', video.deviceName);
        } catch (error) {
          console.error('关闭调试模式失败:', video.deviceName, error);
          // 即使关闭调试模式失败，也要继续关闭视频
        }
      }

      // 移除视频
      this.videos.splice(index, 1);
      console.log('视频已关闭:', video.deviceName);
    },

    // 切换调试面板显示状态
    async toggleDebugPanel(index) {
      const video = this.videos[index];
      if (!video) return;

      const willOpen = !video.showDebugPanel;

      if (willOpen) {
        // 打开调试面板前，先调用开启调试模式接口
        try {
          await this.openDebugModeForDevice(video.deviceSn);
          video.showDebugPanel = true;

          // 如果还没有调试信息，则获取调试信息
          if (!video.debugInfo && !video.debugLoading) {
            this.fetchDebugInfo(index);
          }
        } catch (error) {
          console.error('开启调试模式失败:', error);
          this.$message.error(`开启调试模式失败: ${error.message || '网络错误'}`);
        }
      } else {
        // 关闭调试面板
        this.closeDebugPanel(index);
      }
    },

    // 关闭调试面板
    async closeDebugPanel(index) {
      const video = this.videos[index];
      if (!video) return;

      // 关闭调试面板前，先调用关闭调试模式接口
      try {
        await this.closeDebugModeForDevice(video.deviceSn);
        video.showDebugPanel = false;
      } catch (error) {
        console.error('关闭调试模式失败:', error);
        this.$message.error(`关闭调试模式失败: ${error.message || '网络错误'}`);
        // 即使关闭调试模式失败，也要关闭面板
        video.showDebugPanel = false;
      }
    },

  

    // 获取调试信息
    async fetchDebugInfo(index) {
      const video = this.videos[index];
      if (!video || !video.deviceSn) return;

      video.debugLoading = true;
      video.debugError = null;

      try {
        // 使用API函数发送请求
        const response = await getDebugInfo(video.deviceSn);

        if (response && response.data) {
          video.debugInfo = response.data;
        } else {
          video.debugError = '获取调试信息失败：响应数据为空';
        }
      } catch (error) {
        console.error('获取调试信息失败:', error);
        video.debugError = `获取调试信息失败: ${error.message || '网络错误'}`;
      } finally {
        video.debugLoading = false;
      }
    },

    // 刷新调试信息
    refreshDebugInfo(index) {
      const video = this.videos[index];
      if (video) {
        video.debugInfo = null;
        video.debugError = null;
        this.fetchDebugInfo(index);
      }
    },

    // 格式化调试值显示
    formatDebugValue(value) {
      if (value === null || value === undefined) {
        return 'N/A';
      }
      if (typeof value === 'object') {
        return JSON.stringify(value, null, 2);
      }
      return String(value);
    },

    // 获取开关标签文本
    getSwitchLabel(list, value) {
      if (!list || !Array.isArray(list)) return '';
      const item = list.find(l => l.value === value);
      return item ? item.label : '';
    },

    // 获取开关目标URL
    getSwitchTargetUrl(list, switchValue) {
      if (!list || !Array.isArray(list)) return '';
      // switchValue为true表示要开启，false表示要关闭
      const targetValue = switchValue ? '1' : '0';
      const item = list.find(l => l.value === targetValue);
      return item ? item.url : '';
    },

    // 获取开关目标值
    getSwitchTargetValue(list, switchValue) {
      if (!list || !Array.isArray(list)) return '';
      // switchValue为true表示要开启，false表示要关闭
      const targetValue = switchValue ? '1' : '0';
      const item = list.find(l => l.value === targetValue);
      return item ? item.value : '';
    },

    // 开启调试模式
    async openDebugModeForDevice(deviceSn) {
      if (!deviceSn) {
        throw new Error('设备SN不能为空');
      }

      console.log('开启调试模式:', deviceSn);
      const response = await openDebugMode(deviceSn);

      if (response && response.code === 0) {
        this.$message.success('调试模式已开启');
        return response.data;
      } else {
        throw new Error(response?.message || '开启调试模式失败');
      }
    },

    // 关闭调试模式
    async closeDebugModeForDevice(deviceSn) {
      if (!deviceSn) {
        throw new Error('设备SN不能为空');
      }

      console.log('关闭调试模式:', deviceSn);
      const response = await closeDebugMode(deviceSn);

      if (response && response.code === 0) {
        this.$message.success('调试模式已关闭');
        return response.data;
      } else {
        throw new Error(response?.message || '关闭调试模式失败');
      }
    },

    // 关闭所有打开的调试模式
    async closeAllDebugModes() {
      const openDebugVideos = this.videos.filter(video => video.showDebugPanel);

      if (openDebugVideos.length === 0) {
        return;
      }

      console.log('关闭所有调试模式，共', openDebugVideos.length, '个设备');

      // 并行关闭所有调试模式
      const closePromises = openDebugVideos.map(async (video) => {
        try {
          await this.closeDebugModeForDevice(video.deviceSn);
          console.log('已关闭设备调试模式:', video.deviceName);
        } catch (error) {
          console.error('关闭设备调试模式失败:', video.deviceName, error);
        }
      });

      await Promise.allSettled(closePromises);
    },

    // 处理页面刷新/关闭事件
    handlePageUnload(event) {
      const openDebugVideos = this.videos.filter(video => video.showDebugPanel);

      if (openDebugVideos.length > 0) {
        console.log('页面即将刷新/关闭，关闭所有调试模式');

        // 使用同步方式发送请求，确保在页面关闭前完成
        openDebugVideos.forEach((video) => {
          try {
            // 使用同步XMLHttpRequest确保在页面关闭前完成请求
            this.closeDebugModeSync(video.deviceSn);
          } catch (error) {
            console.error('页面关闭时关闭调试模式失败:', video.deviceName, error);
          }
        });
      }
    },

    // 同步关闭调试模式（用于页面关闭时）
    closeDebugModeSync(deviceSn) {
      try {
        const xhr = new XMLHttpRequest();
        // 使用实际的API路径，需要加上baseURL前缀
        const baseURL = process.env.VUE_APP_BASE_API || '';
        const url = `${baseURL}/control/api/v1/devices/${deviceSn}/jobs/debug_mode_close`;
        xhr.open('POST', url, false); // false表示同步请求
        xhr.setRequestHeader('Content-Type', 'application/json');

        // 获取token
        const token = this.$store.getters.token || getToken();
        if (token) {
          xhr.setRequestHeader('Authorization', `Bearer ${token}`);
        }

        xhr.send();
        console.log('同步关闭调试模式:', deviceSn);
      } catch (error) {
        console.error('同步关闭调试模式失败:', deviceSn, error);
      }
    },

    // 处理开关变化
    async handleSwitchChange(dockSn, url, value) {
      if (!dockSn || !url) {
        console.error('dockSn或url参数缺失');
        return;
      }

      console.log('调用设备控制接口:', { dockSn, url, value });

      try {
        // 调用具体的控制接口
        const response = await sendDebugCommand(dockSn, url, { action: parseInt(value) })
        if (response.code == 0) {
          this.$message.success('操作成功');
        }
      } catch (error) {
        console.error('控制指令发送失败:', error);
        this.$message.error(`操作失败: ${error.message || '网络错误'}`);
      }
    },

    // 处理下拉选择变化
    async handleSelectChange(value, item, type, videoIndex) {
      const video = this.videos[videoIndex];
      if (!video || !video.deviceSn) return;

      // 找到对应的选项
      const targetOption = item.list.find(option => option.value === value);
      if (!targetOption) return;

      try {
        // 发送控制指令
        await this.sendDebugCommand(video.deviceSn, targetOption.url);

        // 更新本地状态
        item.val = value;

        this.$message.success(`${item.title}设置为${targetOption.label}成功`);
      } catch (error) {
        console.error('控制指令发送失败:', error);
        this.$message.error(`${item.title}设置失败: ${error.message}`);
      }
    },

    // 发送调试控制指令
    async sendDebugCommand(deviceSn, commandUrl) {
      // 使用API函数发送控制指令
      const response = await sendDebugCommand({
        dockSn: deviceSn,
        service_identifier: commandUrl,

      });

      return response.data;
    },

    // 调用设备控制API
    async callDeviceControlAPI(dockSn, url, value) {
      if (!dockSn || !url) {
        throw new Error('dockSn和url参数不能为空');
      }

      // 构建完整的API地址
      const apiUrl = `/control/api/v1/devices/${dockSn}/jobs/${url}`;

      console.log('调用设备控制API:', {
        url: apiUrl,
        action: parseInt(value)
      });

      try {
        // 使用axios发送POST请求
        const response = await axios.post(apiUrl, {
          action: parseInt(value) // 将value转换为整数
        });

        if (response && response.data) {
          console.log('设备控制API响应:', response.data);
          return response.data;
        } else {
          throw new Error('API响应数据为空');
        }
      } catch (error) {
        console.error('设备控制API调用失败:', error);
        throw new Error(error.response?.data?.message || error.message || '设备控制失败');
      }
    },

    getDeviceInfo() {
      // 使用 Promise 的 then 方法处理异步操作
      deviceInfo().then(response => {
        console.log("设备信息:", response);

        // 检查响应是否有效
        if (response && response.data) {
          // 将返回的数据赋值给 deviceList
          this.deviceList = null;
          this.deviceList = response.data;
          console.log("设备列表已更新:", this.deviceList);
        } else {
          console.error('获取设备信息失败:', response);
        }
      }).catch(error => {
        console.error('获取设备信息失败:', error);
      });
    },

    // 新增方法：通过经纬度获取城市名称
    async fetchCityNames() {
      const promises = this.coordinates.map(async (coord) => {
        const lng = coord[0];
        const lat = coord[1];
        try {
          const response = await axios.get(`https://res.abeim.cn/api-location_geocoder_address?lng=${lng}&lat=${lat}`);
          if (response.data.code === 200) {
            return response.data.data.city; // 返回城市名称
          }
        } catch (error) {
          console.error('获取城市名称失败:', error);
          return null; // 处理错误
        }
      });

      // 等待所有请求完成
      this.cityNames = [...new Set(await Promise.all(promises))]; // 使用 Set 去重
      console.log('城市名称:', this.cityNames); // 调试用
      this.cityNames.map(async (city) => {
        const response = await rectangle(city);
        console.log(response);
        this.drawRegions(response, this.$refs.amap.map);
      });
    },

    // 判断是否在中国境内
    outOfChina(lng, lat) {
      if (lng < 72.004 || lng > 137.8347) {
        return true;
      }
      if (lat < 0.8293 || lat > 55.8271) {
        return true;
      }
      return false;
    },

    // 转换经纬度
    transformLat(x, y) {
      let ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
      ret += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0;
      ret += (20.0 * Math.sin(y * PI) + 40.0 * Math.sin(y / 3.0 * PI)) * 2.0 / 3.0;
      ret += (160.0 * Math.sin(y / 12.0 * PI) + 320 * Math.sin(y * PI / 30.0)) * 2.0 / 3.0;
      return ret;
    },

    transformLon(x, y) {
      let ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
      ret += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0;
      ret += (20.0 * Math.sin(x * PI) + 40.0 * Math.sin(x / 3.0 * PI)) * 2.0 / 3.0;
      ret += (150.0 * Math.sin(x / 12.0 * PI) + 300.0 * Math.sin(x / 30.0 * PI)) * 2.0 / 3.0;
      return ret;
    },

    // WGS-84到GCJ-02坐标转换
    wgs84ToGcj02(lng, lat) {
      if (this.outOfChina(lng, lat)) {
        return { lng, lat };
      }
      let dLat = this.transformLat(lng - 105.0, lat - 35.0);
      let dLon = this.transformLon(lng - 105.0, lat - 35.0);
      let radLat = lat / 180.0 * PI;
      let magic = Math.sin(radLat);
      magic = 1 - EE * magic * magic;
      let sqrtMagic = Math.sqrt(magic);
      dLat = (dLat * 180.0) / ((A * (1 - EE)) / (magic * sqrtMagic) * PI);
      dLon = (dLon * 180.0) / (A / sqrtMagic * Math.cos(radLat) * PI);
      let mgLat = lat + dLat;
      let mgLng = lng + dLon;
      return { lng: mgLng, lat: mgLat };
    },

    // 创建自定义信息窗体内容
    createInfoWindowContent(name, level, color, height) {
      console.log("创建信息窗体内容", name, level, color, height);
      // 创建一个包含区域信息的自定义内容
      const content = document.createElement('div');
      content.className = 'custom-info-window';

      // 添加区域名称
      const areaTitle = document.createElement('div');
      areaTitle.className = 'info-area-title';
      areaTitle.innerHTML = `<span class="info-label">区域：</span>${name || '未知区域'}`;
      content.appendChild(areaTitle);

      // 根据颜色确定区域类型和样式类
      let areaType = '未知区域';
      let styleClass = 'warning-level';

      if (color === '#DE4329') {
        areaType = '禁飞区';
        styleClass = 'no-fly-zone';
      } else if (color === '#979797') {
        areaType = '限高区';
        styleClass = 'height-restricted-zone';
      } else if (color === '#EE8815') {
        areaType = '加强警示区';
        styleClass = 'warning-zone';
      }

      // 添加等级信息
      const levelInfo = document.createElement('div');
      levelInfo.className = 'info-level';
      levelInfo.innerHTML = `<span class="info-label">等级：</span><span class="${styleClass}">${areaType}</span>`;
      content.appendChild(levelInfo);

      // 如果是限高区，添加限高信息
      if (color === '#979797' && height) {
        const heightInfo = document.createElement('div');
        heightInfo.className = 'info-height';
        heightInfo.innerHTML = `<span class="info-label">限高：</span><span class="height-value">${height}米</span>`;
        content.appendChild(heightInfo);
      }

      return content;
    },

    // 绘制圆形区域和多边形区域的函数
    drawRegions(regions, map) {
      // 首先按面积大小对区域进行排序
      const sortedAreas = [...regions.data.areas].sort((a, b) => {
        // 对于圆形区域，比较半径
        if (a.shape === 0 && b.shape === 0) {
          return b.radius - a.radius; // 大区域在前
        }
        // 对于多边形区域，可以根据点的数量或其他属性排序
        return 0;
      });

      // 按照从大到小的顺序绘制区域，小区域的 zIndex 更高
      sortedAreas.forEach((region2, index) => {
        // 计算 zIndex，小区域有更高的 zIndex
        const baseZIndex = 50;
        const zIndexValue = baseZIndex + index * 10; // 每个区域增加10的zIndex

        if (region2.shape === 0) {
          const gcj02Coord = this.wgs84ToGcj02(region2.lng, region2.lat);
          region2.lng = gcj02Coord.lng;
          region2.lat = gcj02Coord.lat;
          const circle = new AMap.Circle({
            center: [region2.lng, region2.lat],
            radius: region2.radius,
            strokeColor: region2.color,
            strokeWeight: 2,
            fillColor: region2.color,
            fillOpacity: 0.35,
            zIndex: zIndexValue, // 使用计算的 zIndex
            cursor: 'pointer',
            extData: { // 存储区域信息，用于点击事件
              name: region2.name,
              level: region2.level,
              color: region2.color,
              height: region2.height
            }
          });
          circle.setMap(map);

          // 添加点击事件
          circle.on('click', (e) => {
            // 使用存储在 extData 中的信息
            const data = e.target.getExtData();
            console.log("点击圆形区域:", data.name);

            // 更新信息窗体内容
            this.selectedRegion = { name: data.name };
            this.infoWindow.setContent(this.createInfoWindowContent(
              data.name,
              data.level,
              data.color,
              data.height
            ));
            // 在点击位置打开信息窗体
            this.infoWindow.open(map, e.lnglat);

            // 阻止事件冒泡 - 使用正确的方法
            e.stopPropagation && e.stopPropagation(); // 如果存在则调用
            // 或者直接返回 false 也可以阻止冒泡
            return false;
          });
        } else if (region2.shape === 2) {  // 多边形区域
          region2.sub_areas.forEach((region3, subIndex) => {
            // 子区域的 zIndex 应该更高
            const subZIndex = zIndexValue - subIndex - 1;

            if (region3.polygon_points && region3.shape === 1) {
              region3.polygon_points[0].forEach((zb) => {
                const gcj02Coord = this.wgs84ToGcj02(zb[0], zb[1]);
                zb[0] = gcj02Coord.lng;
                zb[1] = gcj02Coord.lat;
              });


              const polygon = new AMap.Polygon({
                path: region3.polygon_points[0],
                strokeColor: region3.color,
                strokeWeight: 1,
                fillColor: region3.color,
                fillOpacity: 0.35,
                zIndex: subZIndex, // 使用更高的 zIndex
                cursor: 'pointer',
                extData: { // 存储区域信息
                  name: region3.name || region2.name, // 优先使用子区域名称
                  level: region3.level || region2.level,
                  color: region3.color,
                  height: region3.height
                }
              });
              polygon.setMap(map);

              // 添加点击事件
              polygon.on('click', (e) => {
                // 使用存储在 extData 中的信息
                const data = e.target.getExtData();
                // 更新信息窗体内容
                this.selectedRegion = { name: data.name };
                this.infoWindow.setContent(this.createInfoWindowContent(
                  data.name,
                  data.level,
                  data.color,
                  data.height
                ));
                // 在点击位置打开信息窗体
                this.infoWindow.open(map, e.lnglat);

                // 阻止事件冒泡
                e.stopPropagation && e.stopPropagation(); // 如果存在则调用
                // 或者直接返回 false 也可以阻止冒泡
                return false;
              });
            } else {
              const polygon1 = new AMap.Circle({
                center: [region2.lng, region2.lat],
                radius: region2.radius,
                strokeColor: region2.color,
                strokeWeight: 2,
                fillColor: region2.color,
                fillOpacity: 0.35,
                zIndex: subZIndex,
                cursor: 'pointer',
                extData: {
                  name: region3.name || region2.name,
                  level: region3.level || region2.level,
                  color: region3.color,
                  height: region3.height
                }
              });
              polygon1.setMap(map);

              polygon1.on('click', (e) => {
                const data = e.target.getExtData();
                // 更新信息窗体内容
                this.selectedRegion = { name: data.name };
                this.infoWindow.setContent(this.createInfoWindowContent(
                  data.name,
                  data.level,
                  data.color,
                  data.height
                ));
                // 在点击位置打开信息窗体
                this.infoWindow.open(map, e.lnglat);

                // 阻止事件冒泡
                e.stopPropagation && e.stopPropagation(); // 如果存在则调用
                // 或者直接返回 false 也可以阻止冒泡
                return false;
              });
            }
          });
        }
      });
    },
  }
}
</script>

<style>
/* 使用 rem 作为基础单位 */
:root {
  --sidebar-width: 17%;
  --card-padding: 1rem;
  --font-size-base: 1rem;
  --icon-size: 2rem;
}

.dockstate {
  justify-content: center;
  align-items: center;
  display: flex;

}


.flight-container {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
}

.map-wrapper {
  flex: 1;
  width: calc(100% - var(--sidebar-width));
  height: 100%;
  position: relative;
}

.flight-sidebar {
  width: var(--sidebar-width);
  height: 100vh;
  background-color: #f5f7fa;
  border-left: 1px solid #dcdfe6;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1000;
}

.flight-sidebar-header {
  height: 3.125rem;
  line-height: 3.125rem;
  padding: 0 0.9375rem;
  font-size: var(--font-size-base);
  font-weight: bold;
  background-color: #fff;
  border-bottom: 1px solid #dcdfe6;
  color: #303133;
}

.flight-sidebar-header .sidebar-icon {
  margin-right: 8px;
  font-size: 18px;
  color: #409EFF;
  vertical-align: middle;
}

.flight-airport-list {
  flex: 1;
  overflow-y: auto;
  padding: calc(var(--card-padding) * 0.625);
}

.flight-airport-item {
  margin-bottom: calc(var(--card-padding) * 0.625);
  border-radius: 1.25rem;
  position: relative;
  cursor: pointer;
}

.flight-airport-item .el-card__body {
  padding: calc(var(--card-padding) * 0.625);
  height: 9rem;
}

.airport-title {
  margin-bottom: calc(var(--card-padding) * 0.2);
  display: flex;
  align-items: center;
}

.airport-divider {
  height: 1px;
  background-color: #EBEEF5;
  margin: 1vh 0;
}

.airport-image {
  width: var(--icon-size);
  height: var(--icon-size);
  text-align: center;
}

.airport-img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
}

.airport-actions {
  margin-top: 10px;
  border-top: 1px solid #ebeef5;
  padding-top: 10px;
  text-align: right;
}

.airport-actions .el-button {
  margin-left: 10px;
}

.amap-logo {
  display: none;
}

/* Element UI 组件样式优化 */
.el-descriptions-item__label {
  color: #606266;
}

.el-descriptions-item__content {
  color: #303133;
}

.el-card {
  border: 1px solid #bac3d7;
}

.el-card.is-hover-shadow:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
}

.locate-button {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  display: none;
}

.flight-airport-item:hover .locate-button {
  display: block;
}

.video-containers-wrapper {
  position: absolute;
  top: 20px;
  left: 20px;
  max-height: calc(100vh - 40px);
  max-width: calc(100% - var(--sidebar-width) - 40px);
  z-index: 2001;
  display: flex;
  flex-flow: column wrap;
  align-content: flex-start;
  gap: 20px;
  overflow-x: auto;
  overflow-y: hidden;
  pointer-events: none;
  /* 允许点击穿透到地图 */
}

.video-container-wrapper {
  display: flex;
  align-items: flex-start;
  pointer-events: auto;
}

.video-container {
  position: relative;
  flex: 0 0 auto;
  width: 400px;
  height: auto;
  pointer-events: auto;
  /* 视频容器可以交互 */
}

/* 调试按钮样式 */
.video-debug-header {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 100;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 4px;
  padding: 2px;
}

.debug-btn {
  color: #fff !important;
  font-size: 12px;
  padding: 4px 8px;
}

.debug-btn:hover {
  color: #409EFF !important;
  background: rgba(64, 158, 255, 0.1);
}

/* 调试面板样式 */
.debug-panel {
  width: 400px;
  margin-left: 10px;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
  max-height: 380px;
  display: flex;
  flex-direction: column;
}

.debug-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  border-radius: 6px 6px 0 0;
}

.debug-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.debug-close-btn {
  color: #909399;
  padding: 0;
  font-size: 16px;
}

.debug-close-btn:hover {
  color: #f56c6c;
}

.debug-panel-content {
  flex: 1;
  padding: 12px;
  overflow-y: auto;
  max-height: 300px;
}

.debug-loading,
.debug-error,
.debug-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
  color: #909399;
}

.debug-loading i,
.debug-error i {
  font-size: 24px;
  margin-bottom: 10px;
}

.debug-error {
  color: #f56c6c;
}

.debug-info {
  font-size: 12px;
}

.debug-sections-horizontal {
  display: flex;
  gap: 15px;
  height: 100%;
}

.debug-section-left,
.debug-section-right {
  flex: 1;
  background: #f8f9fa;
  border-radius: 4px;
  padding: 0;
  overflow: hidden;
}

.debug-section-title {
  background: #e9ecef;
  padding: 8px 12px;
  font-weight: 500;
  color: #495057;
  font-size: 13px;
  text-align: center;
  border-bottom: 1px solid #dee2e6;
}

.debug-control-item {
  padding: 8px 12px;
  border-bottom: 1px solid #e9ecef;
  background: #fff;
  margin: 0;
}

.debug-control-item:last-child {
  border-bottom: none;
}

.debug-control-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.debug-control-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  flex-shrink: 0;
  min-width: 0;
}

.debug-control-widget {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
}

/* 确保TCPlayerVideo组件正确显示 */
.video-container>>>.tcplayer-container {
  width: 400px;
  height: auto;
}

.video-container>>>.video-wrapper {
  height: 250px;
}

.video-container>>>video {
  width: 400px !important;
  height: 250px !important;
}

/* 美化水平滚动条 */
.video-containers-wrapper::-webkit-scrollbar {
  height: 6px;
}

.video-containers-wrapper::-webkit-scrollbar-thumb {
  background: rgba(144, 147, 153, 0.3);
  border-radius: 3px;
}

.video-containers-wrapper::-webkit-scrollbar-track {
  background: transparent;
}

/* 响应式布局 */
@media screen and (max-width: 1920px) {
  :root {
    --sidebar-width: 17%;
    --card-padding: 1rem;
    --font-size-base: 1rem;
    --icon-size: 2rem;
  }
}

@media screen and (max-width: 1600px) {
  :root {
    --sidebar-width: 20%;
    --card-padding: 0.9rem;
    --font-size-base: 0.95rem;
    --icon-size: 1.8rem;
  }
}

@media screen and (max-width: 1366px) {
  :root {
    --sidebar-width: 22%;
    --card-padding: 0.8rem;
    --font-size-base: 0.9rem;
    --icon-size: 1.6rem;
  }

  .task-name {
    font-size: 0.9rem;

  }
}

@media screen and (max-width: 1280px) {
  :root {
    --sidebar-width: 25%;
    --card-padding: 0.7rem;
    --font-size-base: 0.85rem;
    --icon-size: 1.5rem;
  }
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  :root {
    --sidebar-width: 100%;
    --card-padding: 0.6rem;
    --font-size-base: 0.8rem;
    --icon-size: 1.4rem;
  }

  .flight-container {
    flex-direction: column;
  }

  .map-wrapper {
    width: 100%;
    height: 50vh;
  }

  .flight-sidebar {
    width: 100%;
    height: 50vh;
  }
}

.airport-name {
  padding-left: calc(var(--card-padding) * 0.625);
  font-size: var(--font-size-base);
}

.task-name {

  padding-left: 0.3rem;
  line-height: 1.7rem;
  font-size: var(--font-size-base);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: calc(var(--card-padding) * 0.625);
  width: 100%;
}

/* 状态行高度自适应 */
.status-row {
  min-height: 2.5rem;
  padding: calc(var(--card-padding) * 0.3) 0;
}





.el-dialog__wrapper {
  position: fixed !important;
  top: 0;
  left: 0;
  z-index: 2001;
}

.el-dialog {
  margin: 0 !important;
}

/* 第一个对话框位置 */
.left-dialog {
  position: absolute;
  left: 20px !important;
  top: 20px !important;
}

/* 第二个对话框位置 */
.second-dialog {
  position: absolute;
  left: 20px !important;
  top: calc(50% + 20px) !important;
}

/* 添加高亮动画效果 */
@keyframes highlight {
  0% {
    box-shadow: 0 0 0 2px #409EFF;
  }

  100% {
    box-shadow: none;
  }
}

.highlight {
  animation: highlight 1s ease-in-out;
}

/* 自定义信息窗体样式 */
.custom-info-window {
  padding: 12px 15px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  min-width: 180px;
  font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
}

.info-area-title,
.info-level {
  margin-bottom: 8px;
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

.info-label {
  font-weight: bold;
  color: #666;
  margin-right: 5px;
}

.warning-level {
  color: #f56c6c;
  font-weight: 500;
}

/* 隐藏高德地图默认的信息窗体关闭按钮 */
.amap-info-close {
  display: none !important;
}

/* 调整信息窗体箭头样式 */
.amap-info-sharp {
  border-top: 8px solid white !important;
}

/* 区域类型样式 */
.no-fly-zone {
  color: #DE4329;
  font-weight: 500;
}

.height-restricted-zone {
  color: #979797;
  font-weight: 500;
}

.warning-zone {
  color: #ee8815;
  font-weight: 500;
}

.height-value {
  color: #409EFF;
  font-weight: 500;
}

.info-height {
  margin-bottom: 8px;
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

/* 调试按钮样式 */
.video-debug-header {
  position: absolute;
  top: 10px;
  right: 100px;
  z-index: 100;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 4px;
  padding: 2px;
}

.debug-btn {
  color: #fff !important;
  font-size: 12px;
  padding: 4px 8px;
}

.debug-btn:hover {
  color: #409EFF !important;
  background: rgba(64, 158, 255, 0.1);
}

/* 调试面板样式 */
.debug-panel {
  width: 400px;
  margin-left: 10px;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
  max-height: 380px;
  display: flex;
  flex-direction: column;
}

.debug-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  border-radius: 4px 4px 0 0;
}

.debug-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.debug-close-btn {
  color: #909399;
  padding: 0;
  font-size: 16px;
}

.debug-close-btn:hover {
  color: #f56c6c;
}

.debug-panel-content {
  flex: 1;
  padding: 12px;
  overflow-y: auto;
  max-height: 300px;
}

.debug-loading,
.debug-error,
.debug-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
  color: #909399;
}

.debug-loading i,
.debug-error i {
  font-size: 24px;
  margin-bottom: 10px;
}

.debug-error {
  color: #f56c6c;
}

.debug-info {
  font-size: 12px;
}

.debug-sections-horizontal {
  display: flex;
  gap: 15px;
  height: 100%;
}

.debug-section-left,
.debug-section-right {
  flex: 1;
  background: #f8f9fa;
  border-radius: 4px;
  padding: 0;
  overflow: hidden;
}

.debug-section-title {
  background: #e9ecef;
  padding: 8px 12px;
  font-weight: 500;
  color: #495057;
  font-size: 13px;
  text-align: center;
  border-bottom: 1px solid #dee2e6;
}

.debug-control-item {
  padding: 8px 12px;
  border-bottom: 1px solid #e9ecef;
  background: #fff;
  margin: 0;
}

.debug-control-item:last-child {
  border-bottom: none;
}

.debug-control-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.debug-control-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  flex-shrink: 0;
  min-width: 0;
}

.debug-control-widget {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
  width: 60%;
}

.debug-control-widget .el-switch {
  margin: 0;
}

.debug-control-widget .el-select {
  width: 100%;
}

.debug-item {
  display: flex;
  margin-bottom: 8px;
  word-break: break-all;
}

.debug-key {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  margin-right: 8px;
}

.debug-value {
  color: #303133;
  flex: 1;
  white-space: pre-wrap;
}
</style>